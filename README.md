# AS‑RLHF Experiments (v4)

End‑to‑end pipeline for **Activation‑Scoped RLHF** (AS‑PPO on TRL) + baselines, with scripts to:
1) build **capability subspaces** (PCA / CCS / Probe‑U),
2) train **PPO baselines** and **AS‑RLHF** (projection + optional CKA reward shaping),
3) evaluate **WikiText‑103 perplexity**, **GSM8K 5‑shot accuracy**, and **RepTax** (mean 1‑CKA),
4) aggregate logs and **plot** reward–capability Pareto curves and diagnostics.

> Requires a GPU, Python ≥ 3.10, PyTorch ≥ 2.1. Tested with Transformers ≥ 4.42, TRL ≥ 0.9.6.

## Quickstart

```bash
pip install -r requirements.txt
```

### 1) Build capability subspaces
```bash
# Set your Hugging Face token
export HF_TOKEN="*************************************"

# PCA
HF_TOKEN=$HF_TOKEN python scripts/build_subspace.py --method pca --model meta-llama/Meta-Llama-3-8B --tokenizer meta-llama/Meta-Llama-3-8B   --hf1 openai/gsm8k --config1 main --split1 train --field1 question   --hf2 Salesforce/wikitext --config2 wikitext-103-raw-v1 --split2 train --field2 text   --layers 8 16 24 --rank 32 --samples 2000 --max_length 512   --save subspaces/llama3_8b_math_wiki_pca_r32.npz

# CCS (discriminative PCA, target vs background)
HF_TOKEN=$HF_TOKEN python scripts/build_subspace.py --method ccs --model meta-llama/Meta-Llama-3-8B --tokenizer meta-llama/Meta-Llama-3-8B   --hf1 openai/gsm8k --config1 main --split1 train --field1 question   --bg_name Salesforce/wikitext --bg_config wikitext-103-raw-v1 --bg_split train --bg_field text   --layers 8 16 24 --rank 32 --samples 2000 --max_length 512   --save subspaces/llama3_8b_math_ccs_r32.npz

# Probe‑U (linear probe on target vs background features)
HF_TOKEN=$HF_TOKEN python scripts/build_subspace.py --method probe --model meta-llama/Meta-Llama-3-8B --tokenizer meta-llama/Meta-Llama-3-8B   --hf1 openai/gsm8k --config1 main --split1 train --field1 question   --bg_name Salesforce/wikitext --bg_config wikitext-103-raw-v1 --bg_split train --bg_field text   --layers 8 16 24 --rank 32 --samples 2000 --max_length 512   --save subspaces/llama3_8b_math_probe_r32.npz
```

### 2) Train PPO baselines & AS‑RLHF (TRL)
```bash
HF_TOKEN=$HF_TOKEN python scripts/train_trl_asppo.py --config configs/train_ppo_baseline.yaml      # PPO
HF_TOKEN=$HF_TOKEN python scripts/train_trl_asppo.py --config configs/train_ppo_highkl.yaml        # Stronger KL
HF_TOKEN=$HF_TOKEN python scripts/train_trl_asppo.py --config configs/train_ppo_asproj.yaml        # AS-Projection (PCA)
HF_TOKEN=$HF_TOKEN python scripts/train_trl_asppo.py --config configs/train_ppo_asproj_cka.yaml    # AS-Projection + CKA (CCS)
HF_TOKEN=$HF_TOKEN python scripts/train_trl_asppo.py --config configs/train_ppo_ewc.yaml           # EWC baseline
HF_TOKEN=$HF_TOKEN python scripts/train_trl_asppo.py --config configs/train_ppo_onlinegpm.yaml     # Online-GPM baseline
```

*(Optional) DPO generality:*
```bash
HF_TOKEN=$HF_TOKEN python scripts/train_as_dpo.py --config configs/train_dpo_as.yaml
```

### 3) Evaluate
```bash
HF_TOKEN=$HF_TOKEN python scripts/eval_perplexity.py --config configs/eval_ppl.yaml
HF_TOKEN=$HF_TOKEN python scripts/eval_gsm8k.py      --config configs/eval_gsm8k.yaml
HF_TOKEN=$HF_TOKEN python scripts/compute_reptax.py  --config configs/eval_reptax.yaml
```

### 4) Aggregate & plot
```bash
python scripts/collect_metrics.py --runs_dir runs --out_csv results/metrics.csv
python scripts/plot_results.py    --csv results/metrics.csv --out_dir figs
```

Artifacts:
- `figs/pareto_matched_kl.png` – Reward vs capability (matched KL)
- `figs/reward_vs_kl.png` – Reward vs KL (per experiment)
- `figs/removed_energy_violin.png` – Removed‑gradient energy
- `results/metrics.csv` (+ `_steps.csv`) – Aggregated table

MIT License.
