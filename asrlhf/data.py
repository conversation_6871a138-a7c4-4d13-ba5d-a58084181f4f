from typing import Optional
from datasets import load_dataset, Dataset

def load_prompts(dataset_name: str, split: str='train', prompt_field: Optional[str]=None, limit: Optional[int]=None) -> Dataset:
    ds = load_dataset(dataset_name, split=split)
    cols = ds.column_names
    field = prompt_field
    if field is None:
        for cand in ['prompt','question','text','instruction','chosen']:
            if cand in cols:
                field = cand; break
    assert field in cols, f"prompt field {field} not in columns: {cols}"
    ds = ds.rename_column(field, 'prompt')
    keep = ['prompt']
    ds = ds.remove_columns([c for c in ds.column_names if c not in keep])
    if limit is not None:
        ds = ds.select(range(min(limit, len(ds))))
    return ds
