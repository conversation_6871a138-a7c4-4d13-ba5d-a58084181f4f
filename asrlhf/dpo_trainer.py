import os, torch
from typing import Optional
from torch.utils.data import Data<PERSON>oader
from datasets import load_dataset
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
from tqdm import tqdm
from .cka import linear_cka_tokenwise
from .projection import ActivationScopedProjector

def sequence_logprob(logits, labels, mask):
    logp = torch.nn.functional.log_softmax(logits, dim=-1)
    ll = logp.gather(-1, labels.unsqueeze(-1)).squeeze(-1)
    ll = ll * mask
    lens = mask.sum(-1).clamp_min(1)
    return ll.sum(-1) / lens

def tokenize_pairs(tokenizer, batch, max_length: int):
    prompts = batch['prompt']
    chosen = batch['chosen']; rejected = batch['rejected']
    sep = "\n\n"
    ch_inputs = tokenizer([p + sep + c for p,c in zip(prompts, chosen)], return_tensors='pt', padding=True, truncation=True, max_length=max_length)
    rj_inputs = tokenizer([p + sep + r for p,r in zip(prompts, rejected)], return_tensors='pt', padding=True, truncation=True, max_length=max_length)
    p_inputs = tokenizer(prompts, return_tensors='pt', padding=True, truncation=True, max_length=max_length)
    def build_labels(inputs, prompt):
        labels = inputs.input_ids.clone()
        plen = (prompt.attention_mask.sum(dim=1)).tolist()
        mask = torch.zeros_like(labels)
        for i in range(labels.size(0)):
            start = min(plen[i], labels.size(1)-1)
            mask[i, start:] = (inputs.attention_mask[i, start:] == 1).long()
        labels[mask==0] = -100
        return labels, mask
    ch_labels, ch_mask = build_labels(ch_inputs, p_inputs)
    rj_labels, rj_mask = build_labels(rj_inputs, p_inputs)
    return ch_inputs, ch_labels, ch_mask, rj_inputs, rj_labels, rj_mask

def load_pref_triplets(name: str, split: str='train', limit: Optional[int]=None):
    ds = load_dataset(name, split=split)
    cols = ds.column_names
    if all(c in cols for c in ['prompt','chosen','rejected']):
        pass
    elif name.lower().startswith('argilla/ultrafeedback'):
        def _map(ex):
            def join_msgs(msgs):
                if isinstance(msgs, list):
                    return "\n".join([m.get('content','') for m in msgs if isinstance(m, dict)])
                return str(msgs)
            return {'prompt': ex.get('prompt',''),
                    'chosen': join_msgs(ex.get('chosen','')),
                    'rejected': join_msgs(ex.get('rejected',''))}
        ds = ds.map(_map, remove_columns=cols)
    elif 'stack-exchange-paired' in name.lower():
        ds = ds.map(lambda ex: {'prompt': ex['question'], 'chosen': ex['response_j'], 'rejected': ex['response_k']}, remove_columns=cols)
    elif 'hh-rlhf' in name.lower():
        import re
        def split_dialog(s):
            if not isinstance(s, str): return "", str(s)
            parts = [m.start() for m in re.finditer(r'\bAssistant:\b', s)]
            if parts:
                idx = parts[-1]
                return s[:idx], s[idx+len('Assistant:'):].strip()
            return s, ""
        def _map(ex):
            chp, chr_ = split_dialog(ex.get('chosen',''))
            rjp, rjr = split_dialog(ex.get('rejected',''))
            prompt = chp if chp.strip()==rjp.strip() else ""
            chosen = chr_ if prompt else ex.get('chosen','')
            rejected = rjr if prompt else ex.get('rejected','')
            return {'prompt': prompt, 'chosen': chosen, 'rejected': rejected}
        ds = ds.map(_map, remove_columns=cols)
    else:
        raise ValueError(f"Unsupported dataset schema for {name}")
    if limit is not None: ds = ds.select(range(min(limit, len(ds))))
    return ds

class DPOTrainerAS:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
        if self.tok.pad_token is None: self.tok.pad_token = self.tok.eos_token
        self.policy = AutoModelForCausalLM.from_pretrained(cfg['policy_model'],
                        torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None,
                        attn_implementation='sdpa').to(self.device)
        self.policy.config.use_cache = False
        if cfg['use_lora']:
            from peft import LoraConfig, get_peft_model, TaskType
            peft_conf = LoraConfig(task_type=TaskType.CAUSAL_LM, r=cfg['lora_r'], lora_alpha=cfg['lora_alpha'],
                                   lora_dropout=cfg['lora_dropout'], target_modules=cfg['lora_targets'], bias='none')
            self.policy = get_peft_model(self.policy, peft_conf)
        self.ref = AutoModelForCausalLM.from_pretrained(cfg['reference_model'],
                        torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None,
                        attn_implementation='sdpa').to(self.device)
        self.ref.eval()
        # projector
        self.projector = None
        if cfg['subspace_path']:
            import numpy as np
            sub = np.load(cfg['subspace_path'], allow_pickle=True)
            subspaces = {k: sub[k] for k in sub.files if k.startswith('layer')}
            self.projector = ActivationScopedProjector(self.policy, subspaces, hidden_size=self.policy.config.hidden_size, gamma=1.0).register()
        self.opt = torch.optim.AdamW(self.policy.parameters(), lr=cfg['lr'])
        self.ds = load_pref_triplets(cfg['pref_name'], cfg['pref_split'])
        self.loader = DataLoader(self.ds, batch_size=cfg['batch_size'], shuffle=True, collate_fn=lambda feats: {k:[f[k] for f in feats] for k in feats[0].keys()})

    def train(self):
        for ep in range(self.cfg['epochs']):
            for batch in tqdm(self.loader, desc=f"ep {ep+1}/{self.cfg['epochs']}"):
                ch_inputs, ch_labels, ch_mask, rj_inputs, rj_labels, rj_mask = tokenize_pairs(self.tok, batch, self.cfg['max_length'])
                todev = lambda D: {k:v.to(self.device) for k,v in D.items()}
                ch_inputs, rj_inputs = todev(ch_inputs), todev(rj_inputs)
                ch_labels, rj_labels = ch_labels.to(self.device), rj_labels.to(self.device)
                ch_mask, rj_mask = ch_mask.to(self.device), rj_mask.to(self.device)
                if self.projector: self.projector.start_step()
                out_ch = self.policy(**ch_inputs, labels=ch_labels, output_hidden_states=(self.cfg['cka_weight']>0))
                out_rj = self.policy(**rj_inputs, labels=rj_labels, output_hidden_states=(self.cfg['cka_weight']>0))
                with torch.no_grad():
                    ref_ch = self.ref(**ch_inputs, labels=ch_labels)
                    ref_rj = self.ref(**rj_inputs, labels=rj_labels)
                pi_lp_ch = sequence_logprob(out_ch.logits, ch_labels.clamp_min(0), ch_mask.float())
                pi_lp_rj = sequence_logprob(out_rj.logits, rj_labels.clamp_min(0), rj_mask.float())
                rf_lp_ch = sequence_logprob(ref_ch.logits, ch_labels.clamp_min(0), ch_mask.float())
                rf_lp_rj = sequence_logprob(ref_rj.logits, rj_labels.clamp_min(0), rj_mask.float())
                beta = self.cfg['beta']
                loss = -torch.nn.functional.logsigmoid(beta * ((pi_lp_ch - pi_lp_rj) - (rf_lp_ch - rf_lp_rj))).mean()
                # rep penalty (policy vs ref) on chosen branch
                if self.cfg['cka_weight'] > 0:
                    Ls = [-4, -3, -2, -1]
                    with torch.no_grad():
                        ref_h = self.ref(**ch_inputs, output_hidden_states=True).hidden_states
                    ckas = []
                    for L in Ls:
                        for b in range(out_ch.hidden_states[L].size(0)):
                            ckas.append(1.0 - linear_cka_tokenwise(out_ch.hidden_states[L][b].float(), ref_h[L][b].float()))
                    if ckas: loss = loss + self.cfg['cka_weight'] * torch.stack(ckas).mean()
                loss.backward()
                if self.projector: self.projector.end_step()
                self.opt.step(); self.opt.zero_grad()
        os.makedirs(self.cfg['output_dir'], exist_ok=True)
        self.policy.save_pretrained(os.path.join(self.cfg['output_dir'], 'final'))
        self.tok.save_pretrained(os.path.join(self.cfg['output_dir'], 'final'))
