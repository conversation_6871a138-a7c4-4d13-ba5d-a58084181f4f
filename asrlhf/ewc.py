from typing import Dict
import torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm

class EWCRegularizer:
    """Diagonal Fisher EWC on LoRA parameters only, applied via grad hooks.
    Adds 2 * lambda * F * (p - p_star) to gradient during backprop.
    """
    def __init__(self, model, tokenizer, data_name: str, data_config: str, split: str, samples: int, max_length: int, weight: float):
        self.model = model
        self.tokenizer = tokenizer
        self.weight = weight
        self.F = {}
        self.theta_star = {}
        self.hooks = []
        if weight <= 0: return

        device = next(model.parameters()).device
        # cache initial params
        for n,p in model.named_parameters():
            if ('lora_A' in n or 'lora_B' in n) and p.requires_grad:
                self.theta_star[n] = p.detach().clone().to(device)

        # estimate Fisher diag on small LM dataset
        ds = load_dataset(data_name, data_config, split=split) if data_config else load_dataset(data_name, split=split)
        texts = []
        for ex in ds:
            t = ex['text'] if 'text' in ex else list(ex.values())[0]
            if isinstance(t, str): texts.append(t)
            if len(texts) >= samples: break

        self.model.eval()
        with torch.no_grad():
            pass
        # compute Fisher
        print(f"[EWC] Estimating Fisher on {len(texts)} samples ...")
        self.model.train()
        for n in list(self.F.keys()): del self.F[n]
        for start in tqdm(range(0, len(texts), 2), desc='EWC Fisher'):
            batch = texts[start:start+2]
            toks = self.tokenizer(batch, return_tensors='pt', padding=True, truncation=True, max_length=max_length).to(device)
            labels = toks['input_ids'].clone(); labels[toks['attention_mask']==0] = -100
            self.model.zero_grad(set_to_none=True)
            out = self.model(**toks, labels=labels)
            # negative log-likelihood
            loss = out.loss
            loss.backward()
            for n,p in self.model.named_parameters():
                if ('lora_A' in n or 'lora_B' in n) and p.grad is not None:
                    g2 = (p.grad.detach()**2).to('cpu')
                    if n in self.F:
                        self.F[n] += g2
                    else:
                        self.F[n] = g2
        for n in self.F:
            self.F[n] = (self.F[n] / max(1, len(texts)//2)).to('cpu')

    def _ewc_grad(self, name: str, grad: torch.Tensor, param: torch.nn.Parameter) -> torch.Tensor:
        if self.weight <= 0: return grad
        if name not in self.F or name not in self.theta_star: return grad
        F = self.F[name].to(grad.device, dtype=grad.dtype)
        pstar = self.theta_star[name].to(grad.device, dtype=grad.dtype)
        penalty_grad = 2.0 * self.weight * F * (param - pstar)
        return grad + penalty_grad

    def register(self):
        if self.weight <= 0: return self
        for name, p in self.model.named_parameters():
            if ('lora_A' in name or 'lora_B' in name) and p.requires_grad:
                self.hooks.append(p.register_hook(lambda g, n=name, pr=p: self._ewc_grad(n, g, pr)))
        return self

    def remove(self):
        for h in self.hooks: h.remove()
        self.hooks.clear()
