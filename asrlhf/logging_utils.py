import os, json, glob
import pandas as pd

def read_jsonl(path: str):
    rows = []
    with open(path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line: continue
            try:
                rows.append(json.loads(line))
            except Exception:
                pass
    return rows

def collect_runs(runs_dir: str):
    rows = []
    for exp_dir in glob.glob(os.path.join(runs_dir, '*')):
        if not os.path.isdir(exp_dir): continue
        metrics_file = os.path.join(exp_dir, 'metrics.jsonl')
        if os.path.exists(metrics_file):
            rows.extend(read_jsonl(metrics_file))
    return pd.DataFrame(rows)
