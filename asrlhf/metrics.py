import numpy as np, pandas as pd

def matched_kl_bins(df: pd.DataFrame, kl_col='approx_kl', n_bins: int = 10):
    df = df.dropna(subset=[kl_col]).copy()
    df['kl_bin'] = pd.qcut(df[kl_col], q=n_bins, duplicates='drop')
    return df

def length_bins(df: pd.DataFrame, len_col='resp_len_mean', n_bins: int = 10):
    df = df.dropna(subset=[len_col]).copy()
    df['len_bin'] = pd.qcut(df[len_col], q=n_bins, duplicates='drop')
    return df
