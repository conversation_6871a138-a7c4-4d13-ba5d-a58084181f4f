from typing import Optional, List
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from trl import AutoModelForCausalLMWithValueHead
from peft import LoraConfig, get_peft_model, TaskType

DEFAULT_LORA_TARGETS = [
    "q_proj","k_proj","v_proj","o_proj",
    "gate_proj","up_proj","down_proj","query_key_value"
]

def load_tokenizer(name: str):
    tok = AutoTokenizer.from_pretrained(name, use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    return tok

def load_policy_with_value_head(model_name: str, use_lora: bool, lora_r:int, lora_alpha:int, lora_dropout:float, lora_targets: Optional[List[str]] = None):
    model_v = AutoModelForCausalLMWithValueHead.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None,
        attn_implementation='sdpa'
    )
    model_v.config.use_cache = False
    if use_lora:
        targets = lora_targets or DEFAULT_LORA_TARGETS
        peft_conf = LoraConfig(task_type=TaskType.CAUSAL_LM, r=lora_r, lora_alpha=lora_alpha, lora_dropout=lora_dropout, target_modules=targets, bias='none')
        model_v.pretrained_model = get_peft_model(model_v.pretrained_model, peft_conf)
    return model_v

def load_ref_causal(model_name: str):
    ref = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None,
        attn_implementation='sdpa'
    )
    ref.eval()
    return ref
