from typing import Dict, Optional
import torch, numpy as np

class ActivationScopedProjector:
    """Project LoRA gradients onto orthogonal complement of protected subspaces.
    Optionally adds EWC correction to gradients; logs per-step removed-gradient energy.
    """
    def __init__(self, model, subspaces: Dict[str, np.ndarray], hidden_size: int, gamma: float = 1.0,
                 ewc_state: Optional[dict] = None):
        self.model = model
        self.hidden_size = hidden_size
        self.gamma = gamma
        self.P = {}
        for tag, U in subspaces.items():
            if U.size == 0: continue
            U_t = torch.tensor(U, dtype=torch.float32)
            U_t, _ = torch.linalg.qr(U_t, mode='reduced')  # orthonormal
            P = torch.eye(U_t.size(0), dtype=torch.float32) - self.gamma * (U_t @ U_t.T)
            self.P[tag] = P
        self.hooks = []
        self.step_removed_energy = None
        # EWC
        self.ewc = ewc_state  # {'lambda': float, 'theta_star': {name: tensor}, 'F': {name: tensor}}

    def start_step(self):
        self.step_removed_energy = torch.zeros((), dtype=torch.float32, device='cpu')

    def end_step(self):
        val = float(self.step_removed_energy.item()) if self.step_removed_energy is not None else 0.0
        self.step_removed_energy = None
        return val

    def _match_layer_tag(self, name: str) -> Optional[str]:
        for tag in self.P.keys():
            L = int(tag.replace('layer',''))
            if f"layers.{L}." in name:
                return tag
        return None

    def _ewc_grad(self, name: str, param: torch.Tensor) -> Optional[torch.Tensor]:
        if not self.ewc: return None
        lam = float(self.ewc.get('lambda', 0.0))
        if lam <= 0: return None
        F = self.ewc.get('F', {}).get(name, None)
        theta_star = self.ewc.get('theta_star', {}).get(name, None)
        if F is None or theta_star is None: return None
        return lam * F.to(param.device, dtype=param.dtype) * (param - theta_star.to(param.device, dtype=param.dtype))

    def _project_and_accumulate(self, name: str, grad: torch.Tensor, param: torch.Tensor) -> torch.Tensor:
        g = grad
        tag = self._match_layer_tag(name)
        if tag is not None and g is not None:
            P = self.P[tag].to(g.device, dtype=g.dtype)
            if g.ndim == 2:
                out_dim, in_dim = g.shape
                if out_dim == self.hidden_size and in_dim != self.hidden_size:
                    Pg = P @ g  # left
                elif in_dim == self.hidden_size and out_dim != self.hidden_size:
                    Pg = g @ P  # right
                elif in_dim == self.hidden_size and out_dim == self.hidden_size:
                    Pg = P @ g @ P
                else:
                    Pg = g
                if self.step_removed_energy is not None:
                    diff = (g - Pg).detach().float()
                    self.step_removed_energy += (diff * diff).sum().to('cpu')
                g = Pg
        # EWC correction (add gradient of penalty)
        eg = self._ewc_grad(name, param)
        if eg is not None:
            g = g + eg.to(g.device, dtype=g.dtype)
        return g

    def register(self):
        for name, p in self.model.named_parameters():
            if ('lora_A' in name or 'lora_B' in name) and p.requires_grad:
                self.hooks.append(p.register_hook(lambda g, n=name, param=p: self._project_and_accumulate(n, g, param)))
        return self

    def remove(self):
        for h in self.hooks:
            h.remove()
        self.hooks.clear()
