from typing import Dict
import torch

class EWCGradientRegularizer:
    """Lightweight diagonal-Fisher EWC implemented as a gradient hook.
    Adds lambda * F * (theta - theta0) to the gradient on each step.
    """
    def __init__(self, model, weight: float = 0.0):
        self.model = model
        self.weight = weight
        self.theta0 = {}
        self.fisher = {}
        self.hooks = []
        for n,p in self.model.named_parameters():
            if p.requires_grad:
                self.theta0[n] = p.detach().clone().to('cpu')
                self.fisher[n] = torch.zeros_like(p.detach(), device='cpu')
    def accumulate_fisher(self):
        # call this after a PPO step: use squared grad as proxy for Fisher diagonal
        for n,p in self.model.named_parameters():
            if p.grad is None or n not in self.fisher: continue
            g = p.grad.detach().to('cpu')
            self.fisher[n] += g*g
    def normalize_fisher(self, eps: float=1e-8):
        for n in self.fisher:
            self.fisher[n] = self.fisher[n] / (self.fisher[n].abs().max() + eps)
    def register(self):
        if self.weight <= 0: return self
        def add_penalty(grad, name):
            if name not in self.fisher: return grad
            F = self.fisher[name].to(grad.device, dtype=grad.dtype)
            t0 = self.theta0[name].to(grad.device, dtype=grad.dtype)
            return grad + self.weight * F * (self.model.get_parameter(name) - t0)
        for name,p in self.model.named_parameters():
            if p.requires_grad:
                self.hooks.append(p.register_hook(lambda g, n=name: add_penalty(g, n)))
        return self
    def remove(self):
        for h in self.hooks: h.remove()
        self.hooks.clear()
