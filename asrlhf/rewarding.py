from typing import List
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

class RewardScorer:
    def __init__(self, rm_name: str, device: str = 'cuda'):
        self.tok = AutoTokenizer.from_pretrained(rm_name, use_fast=True)
        self.model = AutoModelForSequenceClassification.from_pretrained(
            rm_name, torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None
        ).to(device)
        self.model.eval()
        self.device = device

    @torch.no_grad()
    def score(self, questions: List[str], answers: List[str]) -> torch.Tensor:
        toks = self.tok(questions, answers, return_tensors='pt', padding=True, truncation=True, max_length=2048)
        toks = {k: v.to(self.device) for k, v in toks.items()}
        logits = self.model(**toks).logits.squeeze(-1)  # [B]
        return logits.float()
