from typing import List, Iterable, Dict, Tuple, Optional
import numpy as np, torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm
from sklearn.linear_model import LogisticRegression

@torch.no_grad()
def collect_hidden_states(model, tokenizer, texts: Iterable[str], layers: List[int], max_length: int=512, batch_size:int=2):
    model.eval()
    hs = {L: [] for L in layers}
    buf = []
    for t in texts:
        if not isinstance(t, str): continue
        buf.append(t)
        if len(buf) >= batch_size:
            toks = tokenizer(buf, return_tensors='pt', padding=True, truncation=True, max_length=max_length)
            toks = {k: v.to(model.device) for k, v in toks.items()}
            out = model(**toks, output_hidden_states=True)
            for L in layers:
                H = out.hidden_states[L]
                hs[L].append(H.detach().cpu().reshape(-1, H.size(-1)))
            buf = []
    if buf:
        toks = tokenizer(buf, return_tensors='pt', padding=True, truncation=True, max_length=max_length)
        toks = {k: v.to(model.device) for k, v in toks.items()}
        out = model(**toks, output_hidden_states=True)
        for L in layers:
            H = out.hidden_states[L]
            hs[L].append(H.detach().float().cpu().reshape(-1, H.size(-1)).numpy())
    for L in layers:
        hs[L] = np.concatenate(hs[L], axis=0) if len(hs[L])>0 else np.zeros((0, model.config.hidden_size), dtype=np.float32)
    return hs

def _center(X: np.ndarray): return X - X.mean(0, keepdims=True)

def pca_basis(X: np.ndarray, rank: int) -> np.ndarray:
    if X.shape[0] == 0:
        return np.zeros((X.shape[1], 0), dtype=np.float32)
    Xc = _center(X)
    U, S, VT = np.linalg.svd(Xc, full_matrices=False)
    return VT[:rank].T.astype(np.float32)

def ccs_basis(X_t: np.ndarray, X_b: np.ndarray, rank: int, alpha: float = 1.0) -> np.ndarray:
    d = X_t.shape[1]
    if X_t.shape[0] == 0 or X_b.shape[0] == 0:
        return np.zeros((d, 0), dtype=np.float32)
    Xt = _center(X_t); Xb = _center(X_b)
    St = Xt.T @ Xt / max(1, Xt.shape[0])
    Sb = Xb.T @ Xb / max(1, Xb.shape[0])
    M = St - alpha * Sb
    # symmetric eigendecomposition
    w, V = np.linalg.eigh(M)
    idx = np.argsort(w)[::-1][:rank]
    U = V[:, idx]
    return U.astype(np.float32)

def probe_basis(X_t: np.ndarray, X_b: np.ndarray, rank: int) -> np.ndarray:
    d = X_t.shape[1]
    if X_t.shape[0] == 0 or X_b.shape[0] == 0:
        return np.zeros((d, 0), dtype=np.float32)
    X = np.vstack([X_t, X_b])
    y = np.array([1]*len(X_t) + [0]*len(X_b))
    # simple linear probe (L2-regularized logistic regression)
    clf = LogisticRegression(max_iter=200, n_jobs=None, solver='lbfgs')
    clf.fit(X, y)
    w = clf.coef_.reshape(-1)  # [d]
    # combine weight direction with PCA on class means for rank>1
    mu_t = X_t.mean(0); mu_b = X_b.mean(0)
    D = np.stack([w, mu_t - mu_b], axis=1)  # [d,2]
    # orthonormalize columns
    Q, _ = np.linalg.qr(D)
    if rank <= Q.shape[1]:
        return Q[:, :rank].astype(np.float32)
    # pad with PCA directions
    U_pca = pca_basis(X, rank - Q.shape[1])
    U = np.concatenate([Q, U_pca], axis=1)
    return U[:, :rank].astype(np.float32)

def build_subspaces(method: str, model_name: str, tok_name: str,
                    target_texts: List[str], layers: List[int], rank: int,
                    bg_texts: Optional[List[str]] = None, alpha: float = 1.0,
                    max_length:int=512, sample:int=2000, batch_size:int=2):
    tok = AutoTokenizer.from_pretrained(tok_name, use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None, attn_implementation='sdpa')
    model.to('cuda' if torch.cuda.is_available() else 'cpu')
    if sample and len(target_texts) > sample: target_texts = target_texts[:sample]
    Ht = collect_hidden_states(model, tok, target_texts, layers, max_length=max_length, batch_size=batch_size)
    Hb = None
    if method in ("ccs","probe"):
        assert bg_texts is not None and len(bg_texts)>0, "Background corpus required for CCS/Probe-U"
        if sample and len(bg_texts) > sample: bg_texts = bg_texts[:sample]
        Hb = collect_hidden_states(model, tok, bg_texts, layers, max_length=max_length, batch_size=batch_size)
    bases = {}
    for L in layers:
        if method == "pca":
            U = pca_basis(Ht[L], rank)
        elif method == "ccs":
            U = ccs_basis(Ht[L], Hb[L], rank, alpha=alpha)
        elif method == "probe":
            U = probe_basis(Ht[L], Hb[L], rank)
        else:
            raise ValueError(f"Unknown method {method}")
        bases[f'layer{L}'] = U
    meta = {'model': model_name, 'layers': layers, 'rank': rank, 'hidden_size': model.config.hidden_size, 'method': method}
    return bases, meta
