import os, json, random, torch
from datetime import datetime

def set_seed(seed: int = 42):
    random.seed(seed)
    try:
        import numpy as np
        np.random.seed(seed)
    except Exception:
        pass
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

class JSONLLogger:
    def __init__(self, path: str):
        self.path = path
        os.makedirs(os.path.dirname(path), exist_ok=True)
        if not os.path.exists(path):
            with open(path, 'w') as f: pass

    def log(self, obj: dict):
        obj = dict(obj)
        obj['ts'] = datetime.utcnow().isoformat()
        with open(self.path, 'a') as f:
            f.write(json.dumps(obj) + "\n")
