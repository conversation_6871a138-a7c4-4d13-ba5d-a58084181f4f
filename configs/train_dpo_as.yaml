exp_name: dpo_as
output_dir: runs/dpo_as
policy_model: meta-llama/Meta-Llama-3-8B
reference_model: meta-llama/Meta-Llama-3-8B
tokenizer: meta-llama/Meta-Llama-3-8B
pref_name: argilla/ultrafeedback-binarized-preferences-cleaned
pref_split: train
max_length: 1024
batch_size: 1
grad_accum: 16
epochs: 1
lr: 5e-6
beta: 0.1
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.0
lora_targets: [q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj,query_key_value]
bf16: true
cka_weight: 0.1
cka_every: 50
subspace_path: subspaces/llama3_8b_math_wiki_pca_r32.npz
protected_layers: [8,16,24]
lm_replay_weight: 0.05
replay_name: Salesforce/wikitext
replay_config: wikitext-103-raw-v1
replay_split: train
seed: 42
length_normalize: true
