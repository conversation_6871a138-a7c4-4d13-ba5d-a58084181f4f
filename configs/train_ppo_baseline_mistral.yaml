exp_name: ppo_baseline_mistral ppo_baseline
output_dir: runs/ppo_baseline_mistral runs/ppo_baseline
policy_model: mistralai/Mistral-7B-v0.3
tokenizer: mistralai/Mistral-7B-v0.3
dataset: argilla/ultrafeedback-binarized-preferences-cleaned
split: train
prompt_field: prompt
reward_model: OpenAssistant/reward-model-deberta-v3-large-v2
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
learning_rate: 5e-6
kl_coef: 0.05
epochs: 1
response_length: 128
max_length: 1024
seed: 42
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.0
lora_targets: [q_proj,k_proj,v_proj,o_proj,gate_proj,up_proj,down_proj,query_key_value]
subspace: null
protected_layers: [8,16,24]
cka_weight: 0.0
cka_every: 2
proj_gamma: 1.0
ewc_weight: 0.0
ewc_samples: 256
ewc_dataset: Salesforce/wikitext
ewc_config: wikitext-103-raw-v1
