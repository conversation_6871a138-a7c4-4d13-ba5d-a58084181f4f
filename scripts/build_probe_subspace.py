import argparse, os
from datasets import load_dataset
from asrlhf.subspace import build_probe_subspaces

def iter_texts(name, split, field, config=None, limit=None):
    ds = load_dataset(name, config, split=split) if config else load_dataset(name, split=split)
    assert field in ds.column_names, f"{field} not in {ds.column_names}"
    for i, ex in enumerate(ds):
        if limit and i>=limit: break
        t = ex[field]
        if isinstance(t, str): yield t

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--model', required=True)
    ap.add_argument('--tokenizer', required=True)
    ap.add_argument('--pos_name', required=True)
    ap.add_argument('--pos_field', required=True)
    ap.add_argument('--pos_split', default='train')
    ap.add_argument('--pos_config', default=None)
    ap.add_argument('--neg_name', required=True)
    ap.add_argument('--neg_field', required=True)
    ap.add_argument('--neg_split', default='train')
    ap.add_argument('--neg_config', default=None)
    ap.add_argument('--layers', nargs='+', type=int, required=True)
    ap.add_argument('--rank', type=int, default=16)
    ap.add_argument('--samples', type=int, default=2000)
    ap.add_argument('--max_length', type=int, default=512)
    ap.add_argument('--save', required=True)
    args = ap.parse_args()

    pos_texts = list(iter_texts(args.pos_name, args.pos_split, args.pos_field, args.pos_config, args.samples//2))
    neg_texts = list(iter_texts(args.neg_name, args.neg_split, args.neg_field, args.neg_config, args.samples//2))

    bases, meta = build_probe_subspaces(args.model, args.tokenizer, pos_texts, neg_texts, args.layers, args.rank, max_length=args.max_length, sample=args.samples)
    os.makedirs(os.path.dirname(args.save), exist_ok=True)
    import numpy as np
    np.savez(args.save, **bases, meta=meta)
    print(f"Saved probe-subspaces to {args.save} with meta: {meta}")

if __name__ == "__main__":
    main()
