import argparse, os
import numpy as np

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--template', required=True, help='npz with layer bases to match shapes')
    ap.add_argument('--save', required=True)
    args = ap.parse_args()
    src = np.load(args.template, allow_pickle=True)
    out = {}
    for k in src.files:
        if k.startswith('layer'):
            H, r = src[k].shape
            Q,_ = np.linalg.qr(np.random.randn(H, r))
            out[k] = Q.astype(np.float32)
    out['meta'] = {'type':'random', 'matched_from': args.template}
    os.makedirs(os.path.dirname(args.save), exist_ok=True)
    np.savez(args.save, **out)
    print(f"Saved random-U subspaces to {args.save}")

if __name__ == '__main__':
    main()
