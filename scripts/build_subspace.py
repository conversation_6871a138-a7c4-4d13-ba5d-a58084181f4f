import argparse, os
from datasets import load_dataset
from asrlhf.subspace import build_subspaces

def iter_texts(name, split, field, config=None, limit=None):
    ds = load_dataset(name, config, split=split) if config else load_dataset(name, split=split)
    assert field in ds.column_names, f"{field} not in {ds.column_names}"
    for i, ex in enumerate(ds):
        if limit and i>=limit: break
        t = ex[field]
        if isinstance(t, str): yield t

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--method', choices=['pca','ccs','probe'], required=True)
    ap.add_argument('--model', required=True)
    ap.add_argument('--tokenizer', required=True)
    ap.add_argument('--hf1', required=True); ap.add_argument('--split1', default='train'); ap.add_argument('--field1', required=True); ap.add_argument('--config1', default=None)
    ap.add_argument('--bg_name', default=None); ap.add_argument('--bg_config', default=None); ap.add_argument('--bg_split', default='train'); ap.add_argument('--bg_field', default=None)
    ap.add_argument('--hf2', default=None); ap.add_argument('--split2', default='train'); ap.add_argument('--field2', default=None); ap.add_argument('--config2', default=None)
    ap.add_argument('--layers', nargs='+', type=int, required=True)
    ap.add_argument('--rank', type=int, default=32)
    ap.add_argument('--alpha', type=float, default=1.0, help='CCS background weight')
    ap.add_argument('--samples', type=int, default=2000)
    ap.add_argument('--max_length', type=int, default=512)
    ap.add_argument('--save', required=True)
    args = ap.parse_args()

    texts = list(iter_texts(args.hf1, args.split1, args.field1, args.config1, args.samples))
    if args.hf2 and args.field2:
        texts2 = list(iter_texts(args.hf2, args.split2, args.field2, args.config2, args.samples))
        texts.extend(texts2)

    bg_texts = None
    if args.method in ('ccs','probe'):
        assert args.bg_name and args.bg_field, "Background corpus required for CCS/Probe"
        bg_texts = list(iter_texts(args.bg_name, args.bg_split, args.bg_field, args.bg_config, args.samples))

    bases, meta = build_subspaces(args.method, args.model, args.tokenizer, texts, args.layers, args.rank, bg_texts=bg_texts, alpha=args.alpha, max_length=args.max_length, sample=args.samples)
    os.makedirs(os.path.dirname(args.save), exist_ok=True)
    import numpy as np
    np.savez(args.save, **bases, meta=meta)
    print(f"Saved subspaces to {args.save} with meta: {meta}")

if __name__ == "__main__":
    main()
