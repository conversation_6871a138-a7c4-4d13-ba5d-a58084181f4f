import argparse, os, json
import pandas as pd
from asrlhf.logging_utils import collect_runs

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--runs_dir', required=True)
    ap.add_argument('--out_csv', required=True)
    args = ap.parse_args()

    df = collect_runs(args.runs_dir)
    if df.empty:
        print("No run metrics found.")
        return
    grp = df.groupby('exp_name')
    summary = grp.agg({
        'rm_score_mean': 'mean',
        'reward_shaped_mean': 'mean',
        'approx_kl': 'mean',
        'resp_len_mean': 'mean',
        'removed_grad_energy': 'mean'
    }).reset_index().rename(columns={
        'rm_score_mean': 'RM(mean)',
        'reward_shaped_mean': 'RewardShaped(mean)',
        'approx_kl': 'KL(mean)',
        'resp_len_mean': 'RespLen(mean)',
        'removed_grad_energy': 'RemovedEnergy(mean)'
    })
    os.makedirs(os.path.dirname(args.out_csv), exist_ok=True)
    summary.to_csv(args.out_csv, index=False)
    print(f"Wrote {args.out_csv}")
    steps_csv = args.out_csv.replace('.csv', '_steps.csv')
    df.to_csv(steps_csv, index=False)
    print(f"Wrote {steps_csv}")

if __name__ == '__main__':
    main()
