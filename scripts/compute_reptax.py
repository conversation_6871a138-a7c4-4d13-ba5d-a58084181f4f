import argparse, yaml, pandas as pd, torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM
from asrlhf.cka import linear_cka_tokenwise

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    pol = AutoModelForCausalLM.from_pretrained(cfg['model_path'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None).to('cuda' if torch.cuda.is_available() else 'cpu')
    ref = AutoModelForCausalLM.from_pretrained(cfg['reference_model'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None).to(pol.device)
    pol.eval(); ref.eval()

    ds = load_dataset(cfg['eval_name'], cfg.get('eval_config'), split=cfg['eval_split'])
    layers = cfg['layers']
    rows = []
    for i, ex in enumerate(ds):
        if cfg.get('limit') and i>=cfg['limit']: break
        text = ex.get('text', None)
        if text is None:
            for v in ex.values():
                if isinstance(v, str): text = v; break
        toks = tok(text, return_tensors='pt', truncation=True, max_length=cfg['max_length']).to(pol.device)
        with torch.no_grad():
            Hp = pol(**toks, output_hidden_states=True).hidden_states
            Hr = ref(**toks, output_hidden_states=True).hidden_states
        vals = []
        for L in layers:
            vals.append(1.0 - linear_cka_tokenwise(Hp[L][0].float(), Hr[L][0].float()).item())
        rows.append({'i': i, **{f'layer{L}_1mCKA': v for L,v in zip(layers, vals)}, 'RepTax': sum(vals)/len(vals)})
    df = pd.DataFrame(rows)
    df.to_csv(cfg['out_csv'], index=False)
    print(f"Wrote RepTax to {cfg['out_csv']} (rows={len(df)})")

if __name__ == '__main__':
    main()
