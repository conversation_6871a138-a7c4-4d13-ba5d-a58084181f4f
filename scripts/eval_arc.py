import argparse, yaml, pandas as pd, torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM

def build_question(q, choices):
    letters = ['A','B','C','D','E','F']
    opts = "\n".join([f"{letters[i]}. {c['text']}" for i,c in enumerate(choices)])
    return f"Question: {q}\nOptions:\n{opts}\nAnswer with the letter only."

def choose_letter(tok, model, prompt, choices):
    letters = ['A','B','C','D','E','F'][:len(choices)]
    toks = tok(prompt, return_tensors='pt', truncation=True, max_length=1024).to(model.device)
    with torch.no_grad():
        out = model(**toks)
        last = out.logits[:, -1, :]  # [1,V]
        scores = []
        for L in letters:
            tid = tok(L, add_special_tokens=False)['input_ids']
            if len(tid)==1:
                scores.append(last[0, tid[0]].item())
            else:
                # fallback: generate 1 token
                scores.append(last[0, tid[0]].item())
        idx = int(torch.tensor(scores).argmax().item())
    return letters[idx]

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    ds = load_dataset(cfg['dataset'], cfg['subset'], split=cfg['split'])
    tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    model = AutoModelForCausalLM.from_pretrained(cfg['model_path'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None)
    model.to('cuda' if torch.cuda.is_available() else 'cpu'); model.eval()

    correct = 0; total = 0; rows = []
    for i, ex in enumerate(ds):
        if cfg.get('limit') and i>=cfg['limit']: break
        prompt = build_question(ex['question'], ex['choices'])
        pred = choose_letter(tok, model, prompt, ex['choices'])
        gold = ex['answerKey']
        ok = (pred == gold)
        correct += int(ok); total += 1
        rows.append({'i': i, 'pred': pred, 'gold': gold, 'ok': ok})
        if (i+1) % 50 == 0:
            print(f"{i+1}/{cfg.get('limit', len(ds))} acc={correct/total:.3f}")
    acc = correct / max(1,total)
    print(f"ARC-Challenge acc: {acc:.3f}")
    df = pd.DataFrame(rows); df['acc'] = acc
    df['exp_name'] = cfg['model_path'].split('/')[1] if '/' in cfg['model_path'] else cfg['model_path']
    df.to_csv(cfg['out_csv'], index=False)

if __name__ == '__main__':
    main()
