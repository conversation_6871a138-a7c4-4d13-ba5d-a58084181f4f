import argparse, re, yaml, pandas as pd, numpy as np, torch
from datasets import load_dataset
from transformers import AutoToken<PERSON>, AutoModelForCausalLM

SYS_PROMPT = "You are a careful mathematician. Solve step by step and give the final answer as a number."

def extract_last_number(text: str):
    nums = re.findall(r'-?\d+(?:,\d{3})*(?:\.\d+)?', text.replace(',', ''))
    return nums[-1] if nums else None

def build_fewshot_prompt(examples, question):
    shots = []
    for ex in examples:
        q = ex['question']
        a = ex['answer']
        golds = re.findall(r'####\s*(-?\d+(?:\.\d+)?)', a)
        sol = golds[-1] if golds else a.splitlines()[-1]
        shots.append(f"Q: {q}\nA: {sol}")
    ctx = "\n\n".join(shots)
    return f"{SYS_PROMPT}\n\n{ctx}\n\nQ: {question}\nA:"

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    ds = load_dataset(cfg['dataset'], split=cfg['split'])
    tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    model = AutoModelForCausalLM.from_pretrained(cfg['model_path'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None)
    model.to('cuda' if torch.cuda.is_available() else 'cpu'); model.eval()

    train = load_dataset(cfg['dataset'], split='train')
    few = [train[i] for i in range(min(len(train), cfg['n_shots']))]

    correct = 0; total = 0
    rows = []
    for i, ex in enumerate(ds):
        if cfg.get('limit') and i>=cfg['limit']: break
        prompt = build_fewshot_prompt(few, ex['question'])
        toks = tok(prompt, return_tensors='pt', truncation=True, max_length=2048).to(model.device)
        with torch.no_grad():
            out = model.generate(**toks, max_new_tokens=cfg['max_new_tokens'], temperature=cfg['temperature'], do_sample=False)
        gen = tok.decode(out[0][toks['input_ids'].size(1):], skip_special_tokens=True)
        pred = extract_last_number(gen)
        golds = re.findall(r'####\s*(-?\d+(?:\.\d+)?)', ex['answer'])
        gold = golds[-1] if golds else None
        ok = (pred is not None and gold is not None and str(pred) == str(gold))
        correct += int(ok); total += 1
        rows.append({'i': i, 'pred': pred, 'gold': gold, 'ok': ok})
        if (i+1) % 20 == 0:
            print(f"{i+1} / {cfg.get('limit', len(ds))}  acc={correct/total:.3f}")
    acc = correct / max(1,total)
    print(f"GSM8K 5-shot accuracy: {acc:.3f}")
    df = pd.DataFrame(rows); df['acc'] = acc
    df.to_csv(cfg['out_csv'], index=False)

if __name__ == '__main__':
    main()
