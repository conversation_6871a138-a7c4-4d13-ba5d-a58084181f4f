import argparse, yaml, pandas as pd, torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM

LETTERS = ["A","B","C","D"]

def prompt_item(stem, choices):
    opts = "\n".join([f"{LETTERS[i]}. {choices[i]}" for i in range(len(choices))])
    return f"{stem}\n{opts}\nAnswer with the letter only."

def choose_letter(tok, model, prompt, choices):
    toks = tok(prompt, return_tensors='pt', truncation=True, max_length=1024).to(model.device)
    with torch.no_grad():
        out = model(**toks)
        last = out.logits[:, -1, :]
        scores = [ last[0, tok(L, add_special_tokens=False)['input_ids'][0]].item() for L in LETTERS[:len(choices)] ]
        idx = int(torch.tensor(scores).argmax().item())
    return LETTERS[idx]

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    model = AutoModelForCausalLM.from_pretrained(cfg['model_path'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None)
    model.to('cuda' if torch.cuda.is_available() else 'cpu'); model.eval()

    rows = []
    for subj in cfg['subjects']:
        ds = load_dataset(cfg['dataset'], subj, split='test')
        correct = 0; total = 0
        for i, ex in enumerate(ds):
            if cfg.get('limit_per_subject') and i>=cfg['limit_per_subject']: break
            stem = ex['question']
            choices = [ex['choices'][k] for k in sorted(ex['choices'].keys())]
            prompt = prompt_item(stem, choices)
            pred = choose_letter(tok, model, prompt, choices)
            gold = ex['answer']
            ok = (pred == gold)
            correct += int(ok); total += 1
        acc = correct / max(1,total)
        print(f"{subj}: acc={acc:.3f}")
        rows.append({'subject': subj, 'acc': acc})
    df = pd.DataFrame(rows)
    df['exp_name'] = cfg['model_path'].split('/')[1] if '/' in cfg['model_path'] else cfg['model_path']
    df.to_csv(cfg['out_csv'], index=False)

if __name__ == '__main__':
    main()
