import argparse, math, yaml, pandas as pd
import torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    model = AutoModelForCausalLM.from_pretrained(cfg['model_path'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None)
    model.to('cuda' if torch.cuda.is_available() else 'cpu'); model.eval()

    ds = load_dataset(cfg['eval_name'], cfg.get('eval_config'), split=cfg['eval_split'])
    nll, ntok = 0.0, 0
    rows = []
    for i, ex in enumerate(ds):
        if cfg.get('limit') and i>=cfg['limit']: break
        text = ex.get('text', None)
        if text is None:
            # fallback
            for v in ex.values():
                if isinstance(v, str): text = v; break
        toks = tok(text, return_tensors='pt', truncation=True, max_length=cfg['max_length']).to(model.device)
        labels = toks['input_ids'].clone()
        labels[toks['attention_mask']==0] = -100
        with torch.no_grad():
            out = model(**toks, labels=labels)
        tokens = int(toks['attention_mask'].sum().item())
        nll += out.loss.item() * tokens
        ntok += tokens
        rows.append({'i': i, 'nll': out.loss.item()*tokens, 'tokens': tokens})
    ppl = math.exp(nll / max(1, ntok))
    print(f"Approx perplexity: {ppl:.2f}")
    df = pd.DataFrame(rows); df['ppl'] = ppl
    df.to_csv(cfg['out_csv'], index=False)

if __name__ == '__main__':
    main()
