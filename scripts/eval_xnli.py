import argparse, yaml, pandas as pd, torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM

LABELS = ["entailment","neutral","contradiction"]

def prompt_item(premise, hypothesis, lang='en'):
    return f"Premise: {premise}\nHypothesis: {hypothesis}\nIs the hypothesis entailed by the premise? Answer one word: entailment, neutral, or contradiction."

def choose_label(tok, model, prompt):
    toks = tok(prompt, return_tensors='pt', truncation=True, max_length=1024).to(model.device)
    with torch.no_grad():
        out = model(**toks)
        last = out.logits[:, -1, :]
        scores = [ last[0, tok(lbl, add_special_tokens=False)['input_ids'][0]].item() for lbl in LABELS ]
        idx = int(torch.tensor(scores).argmax().item())
    return LABELS[idx]

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    ds = load_dataset(cfg['dataset'], split=cfg['split'], language=cfg.get('language','en'))
    tok = AutoTokenizer.from_pretrained(cfg['tokenizer'], use_fast=True)
    if tok.pad_token is None: tok.pad_token = tok.eos_token
    model = AutoModelForCausalLM.from_pretrained(cfg['model_path'], torch_dtype=torch.bfloat16 if torch.cuda.is_available() else None)
    model.to('cuda' if torch.cuda.is_available() else 'cpu'); model.eval()

    correct = 0; total = 0; rows = []
    for i, ex in enumerate(ds):
        if cfg.get('limit') and i>=cfg['limit']: break
        prompt = prompt_item(ex['premise'], ex['hypothesis'], cfg.get('language','en'))
        pred = choose_label(tok, model, prompt)
        gold = ex['label']
        gold_str = ['entailment','neutral','contradiction'][gold]
        ok = (pred == gold_str)
        correct += int(ok); total += 1
    acc = correct / max(1,total)
    print(f"XNLI-{cfg.get('language','en')} acc: {acc:.3f}")
    df = pd.DataFrame([{'acc': acc}])
    df['exp_name'] = cfg['model_path'].split('/')[1] if '/' in cfg['model_path'] else cfg['model_path']
    df.to_csv(cfg['out_csv'], index=False)

if __name__ == '__main__':
    main()
