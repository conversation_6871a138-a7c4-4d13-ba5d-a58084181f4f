import argparse, os
import pandas as pd
import matplotlib.pyplot as plt

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--csv', required=True)
    ap.add_argument('--out_dir', required=True)
    args = ap.parse_args()

    os.makedirs(args.out_dir, exist_ok=True)
    df = pd.read_csv(args.csv) if os.path.exists(args.csv) else None
    steps_csv = args.csv.replace('.csv', '_steps.csv')
    steps = pd.read_csv(steps_csv) if os.path.exists(steps_csv) else None

    if df is not None and not df.empty:
        plt.figure()
        x = df['KL(mean)']
        y = df['RewardShaped(mean)']
        plt.scatter(x, y)
        for i, name in enumerate(df['exp_name']):
            plt.annotate(name, (x.iloc[i], y.iloc[i]))
        plt.xlabel('KL (mean)')
        plt.ylabel('Reward (shaped, mean)')
        plt.title('Reward vs KL (per experiment)')
        out1 = os.path.join(args.out_dir, 'reward_vs_kl.png')
        plt.savefig(out1, bbox_inches='tight'); plt.close()

    if steps is not None and not steps.empty:
        plt.figure()
        plt.scatter(steps['approx_kl'], steps['reward_shaped_mean'], alpha=0.5)
        plt.xlabel('approx KL')
        plt.ylabel('reward (shaped)')
        plt.title('Reward–KL scatter (steps)')
        out2 = os.path.join(args.out_dir, 'pareto_matched_kl.png')
        plt.savefig(out2, bbox_inches='tight'); plt.close()

        plt.figure()
        groups = [g['removed_grad_energy'].dropna().values for _, g in steps.groupby('exp_name')]
        labels = [k for k,_ in steps.groupby('exp_name')]
        plt.boxplot(groups, labels=labels, showfliers=False)
        plt.ylabel('Removed-gradient energy')
        plt.title('Constraint cost diagnostic')
        out3 = os.path.join(args.out_dir, 'removed_energy_violin.png')
        plt.savefig(out3, bbox_inches='tight'); plt.close()

    print(f"Wrote figures to {args.out_dir}")

if __name__ == '__main__':
    main()
