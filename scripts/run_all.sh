#!/usr/bin/env bash
set -e

# 1) build subspaces (PCA and CCS)
python scripts/build_subspace.py --method pca   --model meta-llama/Llama-3-8b --tokenizer meta-llama/Llama-3-8b   --hf1 openai/gsm8k --split1 train --field1 question   --hf2 Salesforce/wikitext --config2 wikitext-103-raw-v1 --split2 train --field2 text   --layers 8 16 24 --rank 32 --samples 2000 --max_length 512   --save subspaces/llama3_8b_math_wiki_pca_r32.npz

python scripts/build_subspace.py --method ccs   --model meta-llama/Llama-3-8b --tokenizer meta-llama/Llama-3-8b   --hf1 openai/gsm8k --split1 train --field1 question   --bg_name Salesforce/wikitext --bg_config wikitext-103-raw-v1 --bg_split train --bg_field text   --layers 8 16 24 --rank 32 --samples 2000 --max_length 512   --save subspaces/llama3_8b_math_ccs_r32.npz

# 2) train (subset; expand as needed)
python scripts/train_trl_asppo.py --config configs/train_ppo_baseline.yaml
python scripts/train_trl_asppo.py --config configs/train_ppo_asproj.yaml
python scripts/train_trl_asppo.py --config configs/train_ppo_asproj_cka.yaml

# 3) eval
python scripts/eval_perplexity.py --config configs/eval_ppl.yaml
python scripts/eval_gsm8k.py      --config configs/eval_gsm8k.yaml
python scripts/compute_reptax.py  --config configs/eval_reptax.yaml

# 4) aggregate & plot
python scripts/collect_metrics.py --runs_dir runs --out_csv results/metrics.csv
python scripts/plot_results.py    --csv results/metrics.csv --out_dir figs

echo "Done. See figs/ and results/."
