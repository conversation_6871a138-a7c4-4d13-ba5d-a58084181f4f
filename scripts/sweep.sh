#!/usr/bin/env bash
set -e
# Run 3 seeds for main four methods (baseline, highKL, asproj, asproj+cka)
for SEED in 41 42 43; do
  yq e -i '.seed='${SEED}'' configs/train_ppo_baseline.yaml
  yq e -i '.seed='${SEED}'' configs/train_ppo_highkl.yaml
  yq e -i '.seed='${SEED}'' configs/train_ppo_asproj.yaml
  yq e -i '.seed='${SEED}'' configs/train_ppo_asproj_cka.yaml

  python scripts/train_trl_asppo.py --config configs/train_ppo_baseline.yaml
  python scripts/train_trl_asppo.py --config configs/train_ppo_highkl.yaml
  python scripts/train_trl_asppo.py --config configs/train_ppo_asproj.yaml
  python scripts/train_trl_asppo.py --config configs/train_ppo_asproj_cka.yaml
done
python scripts/collect_metrics.py --runs_dir runs --out_csv results/metrics.csv
python scripts/plot_results.py    --csv results/metrics.csv --out_dir figs
