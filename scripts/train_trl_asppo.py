import argparse, os, yaml, json, torch, numpy as np
from typing import List
from tqdm import tqdm
from trl import PPOConfig, PPOTrainer
from asrlhf.models import load_tokenizer, load_policy_with_value_head, load_ref_causal
from asrlhf.data import load_prompts
from asrlhf.rewarding import RewardScorer
from asrlhf.projection import ActivationScopedProjector
from asrlhf.cka import linear_cka_tokenwise
from asrlhf.utils import J<PERSON><PERSON><PERSON>ogger, set_seed

def compute_cka_penalty(policy_vh, ref_causal, tokenizer, texts: List[str], layers: List[int]):
    device = next(policy_vh.parameters()).device
    toks = tokenizer(texts, return_tensors='pt', padding=True, truncation=True, max_length=2048)
    toks = {k: v.to(device) for k, v in toks.items()}
    with torch.no_grad():
        Hp = policy_vh.pretrained_model(**toks, output_hidden_states=True).hidden_states
        Hr = ref_causal(**toks, output_hidden_states=True).hidden_states
    B = toks['input_ids'].size(0)
    penalties = []
    for b in range(B):
        vals = []
        for L in layers:
            cka = linear_cka_tokenwise(Hp[L][b].float(), Hr[L][b].float())
            vals.append(1.0 - cka)
        penalties.append(torch.stack(vals).mean())
    return torch.stack(penalties)  # [B]

def estimate_diag_fisher(policy_vh, tokenizer, texts: List[str], max_length: int = 1024):
    """Approximate diagonal Fisher on LoRA params using NLL of policy tokens."""
    device = next(policy_vh.parameters()).device
    toks = tokenizer(texts, return_tensors='pt', padding=True, truncation=True, max_length=max_length).to(device)
    labels = toks['input_ids'].clone(); labels[toks['attention_mask']==0] = -100
    out = policy_vh.pretrained_model(**toks, labels=labels)
    loss = out.loss
    policy_vh.zero_grad(set_to_none=True)
    loss.backward()
    F = {}
    with torch.no_grad():
        for n,p in policy_vh.pretrained_model.named_parameters():
            if ('lora_A' in n or 'lora_B' in n) and p.grad is not None:
                F[n] = (p.grad.detach() ** 2).clone()
    policy_vh.zero_grad(set_to_none=True)
    return F

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--config', required=True)
    args = ap.parse_args()
    cfg = yaml.safe_load(open(args.config))

    set_seed(cfg.get('seed', 42))
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    os.makedirs(cfg['output_dir'], exist_ok=True)
    logger = JSONLLogger(os.path.join(cfg['output_dir'], 'metrics.jsonl'))

    tok = load_tokenizer(cfg['tokenizer'])
    policy_vh = load_policy_with_value_head(cfg['policy_model'], cfg['use_lora'], cfg['lora_r'], cfg['lora_alpha'], cfg.get('lora_dropout',0.0), cfg.get('lora_targets'))
    policy_vh.to(device)
    ref_causal = load_ref_causal(cfg['policy_model']).to(device)
    ref_causal.eval()

    # Subspace / projector (and optional EWC)
    projector = None
    ewc_state = None
    if cfg.get('baseline') == 'ewc':
        # snapshot theta*
        theta_star = {n: p.detach().clone().cpu() for n,p in policy_vh.pretrained_model.named_parameters() if ('lora_A' in n or 'lora_B' in n)}
        ewc_state = {'lambda': float(cfg.get('ewc_lambda',50.0)), 'theta_star': theta_star, 'F': {}}
    subspaces = {}
    if cfg.get('subspace'):
        sub = np.load(cfg['subspace'], allow_pickle=True)
        subspaces = {k: sub[k] for k in sub.files if k.startswith('layer')}
    projector = ActivationScopedProjector(policy_vh.pretrained_model, subspaces, hidden_size=policy_vh.pretrained_model.config.hidden_size, gamma=cfg.get('proj_gamma',1.0),
                                          ewc_state=ewc_state).register()

    # TRL PPO config
    ppo_config = PPOConfig(
        model_name=cfg['policy_model'],
        learning_rate=cfg['learning_rate'],
        per_device_train_batch_size=cfg['per_device_train_batch_size'],
        gradient_accumulation_steps=cfg['gradient_accumulation_steps'],
        kl_coef=cfg['kl_coef'],
        seed=cfg.get('seed',42),
        bf16=True,
        num_train_epochs=cfg['epochs'],
        response_length=cfg['response_length'],
    )
    ppo_trainer = PPOTrainer(config=ppo_config, model=policy_vh, tokenizer=tok)

    scorer = RewardScorer(cfg['reward_model'], device=device)
    ds = load_prompts(cfg['dataset'], split=cfg['split'], prompt_field=cfg.get('prompt_field'))

    step_idx = 0
    for epoch in range(cfg['epochs']):
        for start in tqdm(range(0, len(ds), cfg['per_device_train_batch_size']), desc=f"epoch {epoch+1}/{cfg['epochs']}"):
            batch = ds.select(range(start, min(start + cfg['per_device_train_batch_size'], len(ds))))
            prompts = [str(p) for p in batch['prompt']]
            q_toks = tok(prompts, return_tensors='pt', padding=True, truncation=True, max_length=cfg['max_length']).to(device)
            query_tensors = q_toks['input_ids']

            with torch.no_grad():
                response_tensors = ppo_trainer.generate(query_tensors, max_new_tokens=cfg['response_length'], temperature=0.7, top_p=0.9, do_sample=True)
            responses = tok.batch_decode(response_tensors, skip_special_tokens=True)

            with torch.no_grad():
                rm_scores = scorer.score(prompts, responses)  # [B]

            penalty = torch.zeros_like(rm_scores)
            if cfg.get('cka_weight',0.0)>0 and cfg.get('protected_layers'):
                if (step_idx % cfg.get('cka_every',1)) == 0:
                    texts = [ (q + "\n\n" + a) for q, a in zip(prompts, responses) ]
                    penalty = compute_cka_penalty(ppo_trainer.model, ref_causal, tok, texts, cfg['protected_layers'])
            rewards = (rm_scores - cfg.get('cka_weight',0.0) * penalty).detach().to(device)

            # Optional: Online-GPM update (derive subspace from on-policy activations periodically)
            if cfg.get('baseline') == 'online_gpm' and (step_idx % cfg.get('gpm_every',5) == 0):
                # simple: recompute hidden-state PCs for the protected layers on the current batch text
                texts_for_gpm = [ (q + "\n\n" + a) for q, a in zip(prompts, responses) ]
                with torch.no_grad():
                    toks_gpm = tok(texts_for_gpm, return_tensors='pt', padding=True, truncation=True, max_length=cfg['max_length']).to(device)
                    outs = policy_vh.pretrained_model(**toks_gpm, output_hidden_states=True).hidden_states
                # append directions up to gpm_rank
                gpm_rank = int(cfg.get('gpm_rank',32))
                for L in cfg.get('protected_layers',[ ]):
                    H = outs[L].detach().cpu().reshape(-1, outs[L].size(-1))
                    # get top components
                    Hc = H - H.mean(0, keepdims=True)
                    U, S, VT = torch.linalg.svd(torch.tensor(Hc), full_matrices=False)
                    comps = VT[:gpm_rank].T.numpy()
                    tag = f'layer{L}'
                    prev = projector.P.get(tag, None)
                    if prev is None:
                        # initialize from comps
                        U_t = torch.tensor(comps, dtype=torch.float32)
                        U_t, _ = torch.linalg.qr(U_t, mode='reduced')
                        P = torch.eye(U_t.size(0), dtype=torch.float32) - (U_t @ U_t.T)
                        projector.P[tag] = P
                    else:
                        # merge by concatenating and re-orthonormalizing
                        # reconstruct U from P ≈ I - UU^T
                        d = prev.size(0)
                        I = torch.eye(d)
                        # get basis of range(I-P) by eigendecomposition
                        evals, evecs = torch.linalg.eigh(I - prev)
                        keep = (evals > 1e-6)
                        U_old = evecs[:, keep]
                        U_new = torch.tensor(comps, dtype=torch.float32)
                        U_cat = torch.cat([U_old, U_new], dim=1)
                        U_cat, _ = torch.linalg.qr(U_cat, mode='reduced')
                        U_cat = U_cat[:, :min(U_cat.size(1), gpm_rank)]
                        projector.P[tag] = I - (U_cat @ U_cat.T)

            # EWC: periodically update diagonal Fisher
            if cfg.get('baseline') == 'ewc' and (step_idx % cfg.get('ewc_every',1) == 0):
                texts = [ (q + "\n\n" + a) for q, a in zip(prompts, responses) ]
                F = estimate_diag_fisher(ppo_trainer.model, tok, texts, max_length=cfg['max_length'])
                if projector.ewc is not None:
                    for n, Fi in F.items():
                        if n in projector.ewc['F']:
                            projector.ewc['F'][n] = 0.9*projector.ewc['F'][n] + 0.1*Fi.cpu()
                        else:
                            projector.ewc['F'][n] = Fi.cpu()

            if projector: projector.start_step()
            stats = ppo_trainer.step(query_tensors, response_tensors, rewards)
            removed_energy = projector.end_step() if projector else 0.0

            approx_kl = None
            for k in ['objective/kl','ppo/kl','kl','kl_divergence']:
                if k in stats: approx_kl = float(stats[k]); break
            resp_len = float(torch.tensor([len(r) for r in responses]).float().mean().item())
            logger.log({
                'exp_name': cfg['exp_name'],
                'step': step_idx,
                'rm_score_mean': float(rm_scores.mean().item()),
                'reward_shaped_mean': float(rewards.mean().item()),
                'approx_kl': approx_kl,
                'resp_len_mean': resp_len,
                'removed_grad_energy': float(removed_energy),
                'ewc_lambda': float(cfg.get('ewc_lambda',0.0)) if cfg.get('baseline')=='ewc' else 0.0
            })
            step_idx += 1

    save_dir = os.path.join(cfg['output_dir'], 'final')
    os.makedirs(save_dir, exist_ok=True)
    ppo_trainer.save_pretrained(save_dir)
    tok.save_pretrained(save_dir)

if __name__ == '__main__':
    main()
